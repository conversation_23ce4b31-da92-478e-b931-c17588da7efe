# AI NewsHub Dashboard

A modern web application that aggregates the latest news and developments in the AI space from multiple sources in real-time. The dashboard displays AI news items in a clean, tile-based interface and allows filtering by category, time range, and source.

## Features

- **Real-time Data**: Fetches the latest AI news from Twitter, YouTube, Google Search, Brave Search, Reddit, and simulated LinkedIn data
- **Tile-based UI**: Each AI news item is displayed as a tile with a concise headline
- **Detailed View**: Click on a tile to see detailed specifications and highlights
- **Advanced Filtering**: Filter news by category, source, and time range
- **Duplicate Detection**: Intelligent algorithm to remove similar news items from different sources
- **Modern Design**: Clean and responsive UI using Next.js and Tailwind CSS

## Tech Stack

- **Frontend**: Next.js, React, TypeScript
- **Styling**: Tailwind CSS
- **Data Sources**:
  - Twitter API (v2)
  - YouTube API
  - Google Custom Search API
  - Brave Search API
  - Reddit Public API
  - (LinkedIn data is simulated as they don't offer a public API)
- **HTTP Client**: Axios
- **Date Formatting**: date-fns

## Getting Started

### Prerequisites

- Node.js 18 or later
- npm or yarn
- API keys for the services you want to use (application will work with any combination)

### Setting Up API Keys

1. **Twitter API (v2)**: 
   - Go to [Twitter Developer Portal](https://developer.twitter.com/en/portal/dashboard)
   - Create a new project and app
   - Generate a Bearer Token from the app settings
   - Add the Bearer Token to your `.env.local` file

2. **YouTube API**:
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project
   - Enable the YouTube Data API v3
   - Create API credentials (API Key)
   - Add the API Key to your `.env.local` file

3. **Google Custom Search API**:
   - Go to [Google Programmable Search Engine](https://programmablesearchengine.google.com/about/)
   - Create a new search engine
   - Get your Search Engine ID
   - Use the same API Key from Google Cloud Console
   - Add both to your `.env.local` file

4. **Brave Search API**:
   - Go to [Brave Search API](https://brave.com/search/api/)
   - Sign up for an API key
   - Add the API Key to your `.env.local` file

### Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
# Twitter API (v2)
NEXT_PUBLIC_TWITTER_BEARER_TOKEN=your_twitter_api_token

# YouTube API
NEXT_PUBLIC_YOUTUBE_API_KEY=your_youtube_api_key

# Google Custom Search API
NEXT_PUBLIC_GOOGLE_API_KEY=your_google_api_key
NEXT_PUBLIC_GOOGLE_CSE_ID=your_google_cse_id

# Brave Search API
NEXT_PUBLIC_BRAVE_SEARCH_API_KEY=your_brave_search_api_key
```

Note: The application will work even if you don't have all API keys. It will use the ones you provide and fall back to mock data for sources without API keys.

### Installation

1. Clone the repository:
   ```
   git clone https://github.com/yourusername/ai-news-dashboard.git
   cd ai-news-dashboard
   ```

2. Install dependencies:
   ```
   npm install
   # or
   yarn install
   ```

3. Run the development server:
   ```
   npm run dev
   # or
   yarn dev
   ```

4. Open your browser and navigate to `http://localhost:3000`

## Deployment

The application is ready for deployment on platforms like Vercel, Netlify, or any other Next.js compatible hosting service.

1. **Vercel Deployment**:
   ```
   npm install -g vercel
   vercel
   ```

2. **Netlify Deployment**:
   - Build the application: `npm run build`
   - Deploy the `out` directory

## Extending the Application

### Adding Server-Side Functionality

For production use, consider enhancing the application with:

1. **Backend API**: Create a Node.js/Express server to handle API calls and hide your API keys
2. **Web Scraping**: Implement proper scraping for LinkedIn and other sites without public APIs
3. **Caching**: Add Redis or another caching solution to store results and reduce API calls
4. **Database**: Store historical news data in MongoDB or PostgreSQL

### Adding More Data Sources

The modular architecture makes it easy to add more data sources:

1. Create a new fetch function in `src/lib/api.ts`
2. Add the function to the Promise.all array in getNewsItems()
3. Add the source to the getNewsSources() function

## Future Enhancements

- Custom LinkedIn scraper with server-side implementation
- User authentication and saved news items
- Email notifications for important AI news
- Mobile app version
- Dark mode support
- Enhanced categorization using AI/ML
- Sentiment analysis for news items
- RSS feed integration

## License

MIT

## Acknowledgements

- Twitter API for real-time updates
- YouTube API for video content
- Google Custom Search API for web articles
- Brave Search API for comprehensive web results
- Reddit for community discussions
- Tailwind CSS for styling
- Next.js team for the amazing framework 