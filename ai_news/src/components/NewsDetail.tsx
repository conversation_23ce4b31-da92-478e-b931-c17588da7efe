import React from 'react';
import { NewsItem } from '@/types';
import { formatDate } from '@/lib/api';

interface NewsDetailProps {
  item: NewsItem;
  onClose: () => void;
}

const NewsDetail: React.FC<NewsDetailProps> = ({ item, onClose }) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="card w-full max-w-2xl max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-start mb-4">
          <h2 className="card-title">{item.title}</h2>
          <button 
            onClick={onClose}
            className="text-gray-500 hover:text-gray-800"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        
        <div className="card-subtitle flex items-center justify-between">
          <span>{item.source} • {formatDate(item.date)}</span>
          <a 
            href={item.url} 
            target="_blank" 
            rel="noopener noreferrer"
            className="text-primary hover:underline"
          >
            Visit Source
          </a>
        </div>
        
        {item.imageUrl && (
          <div className="mb-4">
            <img 
              src={item.imageUrl} 
              alt={item.title} 
              className="w-full h-48 object-cover rounded-lg"
            />
          </div>
        )}
        
        <div className="card-content mb-4">
          <p className="font-semibold mb-2">{item.description}</p>
          <p>{item.content}</p>
        </div>
        
        <div className="flex flex-wrap gap-2">
          {item.tags.map((tag, index) => (
            <span key={index} className="badge badge-primary">{tag}</span>
          ))}
          <span className="badge badge-secondary ml-auto">{item.category}</span>
        </div>
      </div>
    </div>
  );
};

export default NewsDetail; 