import React from 'react';
import { NewsItem } from '@/types';
import { formatDate } from '@/lib/api';

interface NewsTileProps {
  item: NewsItem;
  onClick: (item: NewsItem) => void;
}

const NewsTile: React.FC<NewsTileProps> = ({ item, onClick }) => {
  return (
    <div 
      className="tile group" 
      onClick={() => onClick(item)}
    >
      <h3 className="tile-title group-hover:text-primary transition-colors">{item.title}</h3>
      <p className="text-sm text-gray-500 mb-2">{item.source} • {formatDate(item.date)}</p>
      <div className="flex items-center justify-between">
        <div className="space-x-1">
          {item.tags.slice(0, 2).map((tag, index) => (
            <span key={index} className="badge badge-primary">{tag}</span>
          ))}
        </div>
        <span className="badge badge-secondary">{item.category}</span>
      </div>
    </div>
  );
};

export default NewsTile; 