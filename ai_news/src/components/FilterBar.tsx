import React, { useEffect, useState } from 'react';
import { FilterOptions, NewsCategory, NewsSource } from '@/types';
import { getNewsSources } from '@/lib/api';

interface FilterBarProps {
  filters: Partial<FilterOptions>;
  onFilterChange: (filters: Partial<FilterOptions>) => void;
}

const categories: NewsCategory[] = [
  'All', 
  'AI Models', 
  'Research Papers', 
  'Tools & Applications', 
  'Companies', 
  'Industry News', 
  'Tutorials', 
  'Events'
];

const timeRanges = [
  { value: 'day', label: 'Today' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'year', label: 'This Year' },
  { value: 'all', label: 'All Time' },
];

const FilterBar: React.FC<FilterBarProps> = ({ filters, onFilterChange }) => {
  const [sources, setSources] = useState<NewsSource[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    const fetchSources = async () => {
      setIsLoading(true);
      try {
        const sourcesData = await getNewsSources();
        setSources(sourcesData);
      } catch (error) {
        console.error('Error fetching sources:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSources();
  }, []);

  const handleSourceToggle = (sourceId: string) => {
    const source = sources.find(s => s.id === sourceId);
    if (!source) return;

    const currentSources = filters.sources || [];
    const sourceName = source.name;
    
    if (currentSources.includes(sourceName)) {
      onFilterChange({
        ...filters,
        sources: currentSources.filter(s => s !== sourceName)
      });
    } else {
      onFilterChange({
        ...filters,
        sources: [...currentSources, sourceName]
      });
    }
  };

  return (
    <div className="bg-white shadow-sm rounded-lg p-4 mb-6">
      <div className="flex flex-col space-y-4">
        <div className="space-y-1">
          <label className="text-sm font-medium text-gray-700">Category</label>
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category}
                className={`px-3 py-1 text-sm rounded-full transition-colors ${
                  filters.category === category 
                    ? 'bg-primary text-white' 
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
                onClick={() => onFilterChange({ ...filters, category })}
              >
                {category}
              </button>
            ))}
          </div>
        </div>
        
        <div className="flex flex-col md:flex-row md:items-end md:justify-between space-y-4 md:space-y-0 md:space-x-4">
          <div className="flex-1">
            <label className="text-sm font-medium text-gray-700">Sources</label>
            <div className="flex flex-wrap gap-2 mt-1">
              {isLoading ? (
                <div className="text-sm text-gray-500">Loading sources...</div>
              ) : (
                sources.map((source) => (
                  <button
                    key={source.id}
                    className={`flex items-center px-3 py-1 text-sm rounded-full transition-colors ${
                      filters.sources?.includes(source.name)
                        ? 'bg-primary text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                    onClick={() => handleSourceToggle(source.id)}
                  >
                    <span className="mr-1">{source.icon}</span>
                    {source.name}
                  </button>
                ))
              )}
            </div>
          </div>
          
          <div className="md:w-48">
            <label className="text-sm font-medium text-gray-700">Time Range</label>
            <select
              className="mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md"
              value={filters.timeRange || 'all'}
              onChange={(e) => onFilterChange({ 
                ...filters, 
                timeRange: e.target.value as FilterOptions['timeRange'] 
              })}
            >
              {timeRanges.map((range) => (
                <option key={range.value} value={range.value}>
                  {range.label}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>
    </div>
  );
};

export default FilterBar; 