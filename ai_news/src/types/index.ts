export interface NewsItem {
  id: string;
  title: string;
  description: string;
  content: string;
  source: string;
  url: string;
  imageUrl?: string;
  date: string;
  category: string;
  tags: string[];
}

export interface NewsSource {
  id: string;
  name: string;
  url: string;
  type: 'article' | 'tweet' | 'video' | 'post';
  icon: string;
}

export type NewsCategory = 
  | 'AI Models' 
  | 'Research Papers' 
  | 'Tools & Applications' 
  | 'Companies' 
  | 'Industry News' 
  | 'Tutorials' 
  | 'Events' 
  | 'All';

export interface FilterOptions {
  category: NewsCategory;
  sources: string[];
  timeRange: 'day' | 'week' | 'month' | 'year' | 'all';
} 