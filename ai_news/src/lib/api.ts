import axios from 'axios';
import { NewsItem, FilterOptions, NewsSource } from '@/types';
import { format, parseISO } from 'date-fns';

// API configuration - in a real app, these would be in environment variables
const TWITTER_BEARER_TOKEN = process.env.NEXT_PUBLIC_TWITTER_BEARER_TOKEN;
const YOUTUBE_API_KEY = process.env.NEXT_PUBLIC_YOUTUBE_API_KEY;
const GOOGLE_CSE_ID = process.env.NEXT_PUBLIC_GOOGLE_CSE_ID;
const GOOGLE_API_KEY = process.env.NEXT_PUBLIC_GOOGLE_API_KEY;
const BRAVE_SEARCH_API_KEY = process.env.NEXT_PUBLIC_BRAVE_SEARCH_API_KEY;

// Helper function to create unique IDs
const createId = (): string => Math.random().toString(36).substring(2, 9);

// Mock data for when API keys aren't configured
const mockNewsItems: NewsItem[] = [
  {
    id: '1',
    title: 'OpenAI Releases GPT-5 with Breakthrough Reasoning Capabilities',
    description: 'The latest model shows 40% improvement in complex reasoning tasks.',
    content: 'OpenAI has announced GPT-5, their most advanced AI model to date. The new model demonstrates significant improvements in reasoning, code generation, and multimodal understanding. Independent evaluations show a 40% improvement in complex reasoning tasks compared to GPT-4.',
    source: 'Twitter - AI News Daily',
    url: 'https://twitter.com/aiTechDaily/status/1732456789',
    imageUrl: 'https://via.placeholder.com/300x200?text=GPT-5',
    date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
    category: 'AI Models',
    tags: ['GPT-5', 'OpenAI', 'Language Models', 'Twitter']
  },
  {
    id: '2',
    title: 'Google DeepMind Introduces AlphaCode 2 for Advanced Programming',
    description: 'New AI system can solve competitive programming problems at expert level.',
    content: 'Google DeepMind has unveiled AlphaCode 2, a next-generation AI system designed to write computer programs. The system can solve competitive programming problems at an expert level, outperforming 85% of human participants in programming contests.',
    source: 'YouTube - Google AI',
    url: 'https://www.youtube.com/watch?v=example123',
    imageUrl: 'https://via.placeholder.com/300x200?text=AlphaCode+2',
    date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(), // 4 days ago
    category: 'AI Models',
    tags: ['AlphaCode', 'Google DeepMind', 'Programming AI', 'YouTube', 'Video']
  },
  {
    id: '3',
    title: 'Meta Releases LLAMA 3 as Open Source',
    description: 'High-performance large language model now available for researchers and developers.',
    content: 'Meta has released LLAMA 3, its latest large language model, as open source. The model is available in several sizes, from 7B to 70B parameters, and shows competitive performance with proprietary models. Researchers and developers can now access the model weights and integrate them into their applications.',
    source: 'Google - TechCrunch',
    url: 'https://techcrunch.com/example/llama3',
    imageUrl: 'https://via.placeholder.com/300x200?text=LLAMA+3',
    date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
    category: 'AI Models',
    tags: ['LLAMA 3', 'Meta AI', 'Open Source AI', 'Google']
  },
  {
    id: '4',
    title: 'New Research Shows AI Can Detect Early Signs of Alzheimer Disease',
    description: 'Deep learning algorithm identifies subtle patterns in brain scans years before symptoms appear.',
    content: 'Researchers from Stanford University have developed a deep learning algorithm that can detect early signs of Alzheimer disease in brain scans. The algorithm can identify subtle patterns up to six years before clinical symptoms appear, potentially allowing for earlier intervention and treatment.',
    source: 'Google - Medical AI Journal',
    url: 'https://medicalaijournal.com/example/alzheimers',
    imageUrl: 'https://via.placeholder.com/300x200?text=AI+in+Medicine',
    date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days ago
    category: 'Research Papers',
    tags: ['Healthcare AI', 'Medical Imaging', 'Neural Networks', 'Google']
  },
  {
    id: '5',
    title: 'Anthropic Secures $1B in Funding for Constitutional AI Research',
    description: 'Investment will accelerate development of safer AI systems.',
    content: 'Anthropic has secured $1 billion in new funding to continue its work on Constitutional AI, an approach to training AI systems that adhere to a set of principles. The company will use the funding to scale its research efforts and expand its team of AI safety researchers.',
    source: 'Twitter - Tech Investor',
    url: 'https://twitter.com/techinvestor/status/**********',
    imageUrl: 'https://via.placeholder.com/300x200?text=Anthropic',
    date: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(), // 12 days ago
    category: 'Companies',
    tags: ['Anthropic', 'AI Safety', 'Investment', 'Twitter']
  },
  {
    id: '6',
    title: 'New Tool Converts Natural Language to SQL Queries with 98% Accuracy',
    description: 'Open-source project simplifies database interactions for non-technical users.',
    content: 'A new open-source tool called SQLGenius can convert natural language to SQL queries with 98% accuracy. The tool uses a fine-tuned large language model specifically optimized for database operations, making it easier for non-technical users to interact with databases without knowing SQL syntax.',
    source: 'YouTube - Database Tech',
    url: 'https://www.youtube.com/watch?v=sqlgenius123',
    imageUrl: 'https://via.placeholder.com/300x200?text=SQLGenius',
    date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
    category: 'Tools & Applications',
    tags: ['SQL', 'Natural Language Processing', 'Developer Tools', 'YouTube', 'Video']
  },
  {
    id: '7',
    title: 'Tesla Unveils New AI-Powered Robotaxi Service',
    description: 'Autonomous taxis to launch in three major U.S. cities next year.',
    content: 'Tesla has unveiled its AI-powered Robotaxi service, set to launch in San Francisco, Austin, and Miami next year. The vehicles will operate without human drivers, using Tesla FSD (Full Self-Driving) AI system. The company claims the service will be 50% cheaper than current ride-sharing options.',
    source: 'Google - Electric Vehicle News',
    url: 'https://evnews.com/example/tesla-robotaxi',
    imageUrl: 'https://via.placeholder.com/300x200?text=Tesla+Robotaxi',
    date: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000).toISOString(), // 18 days ago
    category: 'Industry News',
    tags: ['Tesla', 'Autonomous Vehicles', 'FSD', 'Google']
  },
  {
    id: '8',
    title: 'IBM Watson Assistant Now Supports 45 Languages',
    description: 'Major upgrade expands global accessibility of conversational AI platform.',
    content: 'IBM has announced a major upgrade to Watson Assistant, its conversational AI platform, which now supports 45 languages. The expansion includes improved understanding of regional dialects and colloquialisms, making the platform more accessible to businesses around the world.',
    source: 'Twitter - IBM Developer',
    url: 'https://twitter.com/ibmdev/status/1725432109',
    imageUrl: 'https://via.placeholder.com/300x200?text=IBM+Watson',
    date: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(), // 21 days ago
    category: 'Tools & Applications',
    tags: ['IBM Watson', 'Multilingual AI', 'Conversational AI', 'Twitter']
  },
];

// Twitter API service (X)
async function fetchTwitterPosts(query: string): Promise<NewsItem[]> {
  try {
    if (!TWITTER_BEARER_TOKEN) {
      console.warn('Twitter API token not configured');
      return mockNewsItems.filter(item => item.source.includes('Twitter'));
    }

    // Twitter v2 API to search for recent tweets
    const response = await axios.get(
      `https://api.twitter.com/2/tweets/search/recent?query=${encodeURIComponent(query)}&tweet.fields=created_at,public_metrics,entities&expansions=author_id&user.fields=name,profile_image_url`,
      {
        headers: {
          Authorization: `Bearer ${TWITTER_BEARER_TOKEN}`
        }
      }
    );

    if (!response.data.data || response.data.data.length === 0) {
      return [];
    }

    const users = response.data.includes.users.reduce((acc: any, user: any) => {
      acc[user.id] = user;
      return acc;
    }, {});

    return response.data.data.map((tweet: any) => {
      const user = users[tweet.author_id];
      const tags = tweet.entities?.hashtags 
        ? tweet.entities.hashtags.map((tag: any) => tag.tag) 
        : [];
      
      return {
        id: createId(),
        title: tweet.text.length > 60 ? tweet.text.substring(0, 60) + '...' : tweet.text,
        description: tweet.text,
        content: tweet.text,
        source: `Twitter - ${user.name}`,
        url: `https://twitter.com/${user.username}/status/${tweet.id}`,
        imageUrl: user.profile_image_url,
        date: tweet.created_at,
        category: 'Industry News',
        tags: [...tags, 'Twitter']
      };
    });
  } catch (error) {
    console.error('Error fetching Twitter data:', error);
    return mockNewsItems.filter(item => item.source.includes('Twitter'));
  }
}

// YouTube API service
async function fetchYouTubeVideos(query: string): Promise<NewsItem[]> {
  try {
    if (!YOUTUBE_API_KEY) {
      console.warn('YouTube API key not configured');
      return mockNewsItems.filter(item => item.source.includes('YouTube'));
    }

    const response = await axios.get(
      `https://www.googleapis.com/youtube/v3/search?part=snippet&q=${encodeURIComponent(query)}&type=video&maxResults=10&order=date&relevanceLanguage=en&key=${YOUTUBE_API_KEY}`
    );

    if (!response.data.items || response.data.items.length === 0) {
      return [];
    }

    return response.data.items.map((item: any) => {
      const { snippet, id } = item;
      
      return {
        id: createId(),
        title: snippet.title,
        description: snippet.description,
        content: snippet.description,
        source: `YouTube - ${snippet.channelTitle}`,
        url: `https://www.youtube.com/watch?v=${id.videoId}`,
        imageUrl: snippet.thumbnails.medium.url,
        date: snippet.publishedAt,
        category: 'Tutorials',
        tags: ['YouTube', 'Video', 'AI']
      };
    });
  } catch (error) {
    console.error('Error fetching YouTube data:', error);
    return mockNewsItems.filter(item => item.source.includes('YouTube'));
  }
}

// Brave Search API service
async function fetchBraveSearch(query: string): Promise<NewsItem[]> {
  try {
    if (!BRAVE_SEARCH_API_KEY) {
      console.warn('Brave Search API key not configured');
      return mockNewsItems.filter(item => item.source.includes('Brave'));
    }

    const response = await axios.get(
      `https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(query)}&search_lang=en&count=10&freshness=week`,
      {
        headers: {
          'Accept': 'application/json',
          'Accept-Encoding': 'gzip',
          'X-Subscription-Token': BRAVE_SEARCH_API_KEY
        }
      }
    );

    if (!response.data.web?.results || response.data.web.results.length === 0) {
      return [];
    }

    return response.data.web.results.map((item: any) => {
      // Try to determine a category based on keywords in the title or description
      let category = 'Industry News';
      const lowerTitle = item.title.toLowerCase();
      const lowerDescription = item.description.toLowerCase();
      
      if (lowerTitle.includes('research') || lowerDescription.includes('research')) {
        category = 'Research Papers';
      } else if (lowerTitle.includes('model') || lowerDescription.includes('model')) {
        category = 'AI Models';
      } else if (lowerTitle.includes('tool') || lowerDescription.includes('tool')) {
        category = 'Tools & Applications';
      } else if (lowerTitle.includes('company') || lowerDescription.includes('company')) {
        category = 'Companies';
      }
      
      return {
        id: createId(),
        title: item.title,
        description: item.description,
        content: item.description,
        source: `Brave - ${new URL(item.url).hostname.replace('www.', '')}`,
        url: item.url,
        imageUrl: item.thumbnail?.src || null,
        date: item.age ? new Date(Date.now() - (parseInt(item.age) * 1000)).toISOString() : new Date().toISOString(),
        category,
        tags: ['Brave', category, 'Web']
      };
    });
  } catch (error) {
    console.error('Error fetching Brave Search data:', error);
    return mockNewsItems.filter(item => item.source.includes('Brave'));
  }
}

// LinkedIn News Scraper (Note: LinkedIn doesn't offer a public API for this)
// This is a simulated version that would need to be replaced with a proper server-side scraper
async function fetchLinkedInNews(query: string): Promise<NewsItem[]> {
  try {
    // In a real app, this would be a server-side API endpoint that scrapes LinkedIn
    // Since LinkedIn doesn't have a public API for this purpose, we'd need to implement
    // a custom scraper on the backend
    
    // For now, return mock data for LinkedIn
    return mockNewsItems
      .filter(item => item.category === 'Companies' || item.category === 'Industry News')
      .map(item => ({
        ...item,
        source: `LinkedIn - ${item.source.split(' - ')[1] || 'Business News'}`,
        tags: [...item.tags.filter(tag => tag !== 'Twitter' && tag !== 'Google'), 'LinkedIn']
      }));
  } catch (error) {
    console.error('Error fetching LinkedIn data:', error);
    return [];
  }
}

// Google Custom Search API service
async function fetchGoogleNews(query: string): Promise<NewsItem[]> {
  try {
    if (!GOOGLE_API_KEY || !GOOGLE_CSE_ID) {
      console.warn('Google API credentials not configured');
      return mockNewsItems.filter(item => item.source.includes('Google'));
    }

    const response = await axios.get(
      `https://www.googleapis.com/customsearch/v1?key=${GOOGLE_API_KEY}&cx=${GOOGLE_CSE_ID}&q=${encodeURIComponent(query)}&sort=date&dateRestrict=m1`
    );

    if (!response.data.items || response.data.items.length === 0) {
      return [];
    }

    return response.data.items.map((item: any) => {
      // Try to determine a category based on keywords in the title or snippet
      let category = 'Industry News';
      if (item.title.toLowerCase().includes('research') || item.snippet.toLowerCase().includes('research')) {
        category = 'Research Papers';
      } else if (item.title.toLowerCase().includes('model') || item.snippet.toLowerCase().includes('model')) {
        category = 'AI Models';
      } else if (item.title.toLowerCase().includes('tool') || item.snippet.toLowerCase().includes('tool')) {
        category = 'Tools & Applications';
      }
      
      return {
        id: createId(),
        title: item.title,
        description: item.snippet,
        content: item.snippet,
        source: `Google - ${new URL(item.link).hostname.replace('www.', '')}`,
        url: item.link,
        imageUrl: item.pagemap?.cse_image?.[0]?.src || null,
        date: item.pagemap?.metatags?.[0]?.['article:published_time'] || new Date().toISOString(),
        category,
        tags: ['Google', category]
      };
    });
  } catch (error) {
    console.error('Error fetching Google data:', error);
    return mockNewsItems.filter(item => item.source.includes('Google'));
  }
}

// Reddit API service for AI subreddits
async function fetchRedditPosts(query: string): Promise<NewsItem[]> {
  try {
    // Reddit doesn't require auth for this basic endpoint
    const subreddits = ['artificial', 'MachineLearning', 'AINews', 'GPT3'];
    const allPosts: NewsItem[] = [];
    
    for (const subreddit of subreddits) {
      try {
        const response = await axios.get(
          `https://www.reddit.com/r/${subreddit}/search.json?q=${encodeURIComponent(query)}&restrict_sr=on&sort=new&t=week&limit=5`
        );
        
        if (response.data?.data?.children) {
          const posts = response.data.data.children
            .filter((post: any) => post.data && !post.data.is_self)
            .map((post: any) => {
              const data = post.data;
              let category = 'Industry News';
              
              if (data.link_flair_text) {
                const flair = data.link_flair_text.toLowerCase();
                if (flair.includes('research')) {
                  category = 'Research Papers';
                } else if (flair.includes('project') || flair.includes('tool')) {
                  category = 'Tools & Applications';
                } else if (flair.includes('discussion')) {
                  category = 'Industry News';
                }
              }
              
              return {
                id: createId(),
                title: data.title,
                description: data.selftext || `Posted by u/${data.author} in r/${data.subreddit}`,
                content: data.selftext || data.url,
                source: `Reddit - r/${data.subreddit}`,
                url: `https://www.reddit.com${data.permalink}`,
                imageUrl: data.thumbnail !== 'self' && data.thumbnail !== 'default' ? data.thumbnail : null,
                date: new Date(data.created_utc * 1000).toISOString(),
                category,
                tags: ['Reddit', data.subreddit, category]
              };
            });
          
          allPosts.push(...posts);
        }
      } catch (error) {
        console.error(`Error fetching from r/${subreddit}:`, error);
      }
    }
    
    return allPosts;
  } catch (error) {
    console.error('Error fetching Reddit data:', error);
    return [];
  }
}

// Main API function to fetch all news sources
export async function getNewsItems(filters?: Partial<FilterOptions>): Promise<NewsItem[]> {
  try {
    // Define search queries for AI news
    const query = 'artificial intelligence OR machine learning OR AI models OR deep learning OR LLM OR GPT OR language model OR neural network OR AI research';
    
    let allItems: NewsItem[] = [];
    
    // Check if any API keys are configured
    const hasApiKeys = TWITTER_BEARER_TOKEN || 
                      YOUTUBE_API_KEY || 
                      (GOOGLE_API_KEY && GOOGLE_CSE_ID) ||
                      BRAVE_SEARCH_API_KEY;
    
    if (hasApiKeys) {
      // Fetch data from multiple sources in parallel
      const [twitterPosts, youtubeVideos, googleNews, braveResults, redditPosts, linkedInNews] = await Promise.all([
        fetchTwitterPosts(query),
        fetchYouTubeVideos(query),
        fetchGoogleNews(query),
        fetchBraveSearch(query),
        fetchRedditPosts(query),
        fetchLinkedInNews(query)
      ]);
      
      // Combine all results
      allItems = [
        ...twitterPosts, 
        ...youtubeVideos, 
        ...googleNews, 
        ...braveResults, 
        ...redditPosts,
        ...linkedInNews
      ];
    } else {
      // If no API keys are configured, use mock data
      console.warn('No API keys configured, using mock data');
      allItems = [...mockNewsItems];
    }
    
    // Apply filters
    if (filters) {
      if (filters.category && filters.category !== 'All') {
        allItems = allItems.filter(item => item.category === filters.category);
      }
      
      if (filters.sources && filters.sources.length > 0) {
        allItems = allItems.filter(item => {
          const itemSource = item.source.split(' - ')[0].toLowerCase();
          return filters.sources!.some(source => source.toLowerCase() === itemSource);
        });
      }
      
      if (filters.timeRange && filters.timeRange !== 'all') {
        const now = new Date();
        let cutoffDate = new Date();
        
        switch (filters.timeRange) {
          case 'day':
            cutoffDate.setDate(now.getDate() - 1);
            break;
          case 'week':
            cutoffDate.setDate(now.getDate() - 7);
            break;
          case 'month':
            cutoffDate.setMonth(now.getMonth() - 1);
            break;
          case 'year':
            cutoffDate.setFullYear(now.getFullYear() - 1);
            break;
        }
        
        allItems = allItems.filter(item => new Date(item.date) >= cutoffDate);
      }
    }
    
    // Remove duplicates (based on title similarity)
    const uniqueItems: NewsItem[] = [];
    const titles = new Set<string>();
    
    for (const item of allItems) {
      // Normalize title for comparison
      const normalizedTitle = item.title.toLowerCase().replace(/\W+/g, ' ').trim();
      
      // Check if we already have a similar title
      let isDuplicate = false;
      for (const existingTitle of titles) {
        // Simple string similarity check
        if (normalizedTitle.includes(existingTitle) || existingTitle.includes(normalizedTitle)) {
          isDuplicate = true;
          break;
        }
      }
      
      if (!isDuplicate) {
        titles.add(normalizedTitle);
        uniqueItems.push(item);
      }
    }
    
    // Sort by date (newest first)
    return uniqueItems.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  } catch (error) {
    console.error('Error fetching news data:', error);
    return mockNewsItems;
  }
}

// Get a specific news item by ID
export async function getNewsItemById(id: string): Promise<NewsItem | null> {
  try {
    // In a real app, you would fetch this from a database or API
    // For now, we'll fetch all items and find the matching one
    const allItems = await getNewsItems();
    return allItems.find(item => item.id === id) || null;
  } catch (error) {
    console.error('Error fetching news item by ID:', error);
    return null;
  }
}

// Get available news sources
export async function getNewsSources(): Promise<NewsSource[]> {
  return [
    { id: '1', name: 'Twitter', url: 'https://twitter.com', type: 'tweet', icon: '🐦' },
    { id: '2', name: 'YouTube', url: 'https://youtube.com', type: 'video', icon: '📺' },
    { id: '3', name: 'Google', url: 'https://google.com', type: 'article', icon: '🔍' },
    { id: '4', name: 'Brave', url: 'https://search.brave.com', type: 'article', icon: '🦁' },
    { id: '5', name: 'Reddit', url: 'https://reddit.com', type: 'post', icon: '👽' },
    { id: '6', name: 'LinkedIn', url: 'https://linkedin.com', type: 'post', icon: '💼' }
  ];
}

// Format date for display
export function formatDate(dateString: string): string {
  try {
    const date = parseISO(dateString);
    return format(date, 'MMM d, yyyy');
  } catch (error) {
    // Fallback if parsing fails
    return dateString;
  }
} 