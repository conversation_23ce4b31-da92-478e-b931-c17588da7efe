'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import NewsTile from '@/components/NewsTile';
import NewsDetail from '@/components/NewsDetail';
import FilterBar from '@/components/FilterBar';
import { getNewsItems } from '@/lib/api';
import { NewsItem, FilterOptions, NewsCategory } from '@/types';

export default function Home() {
  const [newsItems, setNewsItems] = useState<NewsItem[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedItem, setSelectedItem] = useState<NewsItem | null>(null);
  const [filters, setFilters] = useState<Partial<FilterOptions>>({
    category: 'All',
    timeRange: 'all',
    sources: [],
  });

  useEffect(() => {
    const fetchNews = async () => {
      setLoading(true);
      setError(null);
      
      try {
        const items = await getNewsItems(filters);
        setNewsItems(items);
      } catch (error) {
        console.error('Error fetching news:', error);
        setError('Failed to load news. Please check your API keys and try again.');
      } finally {
        setLoading(false);
      }
    };

    fetchNews();
  }, [filters]);

  const handleTileClick = (item: NewsItem) => {
    setSelectedItem(item);
  };

  const handleCloseDetail = () => {
    setSelectedItem(null);
  };

  const handleFilterChange = (newFilters: Partial<FilterOptions>) => {
    setFilters(newFilters);
  };

  return (
    <Layout>
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">AI News Dashboard</h1>
        <p className="text-gray-600">Stay updated with the latest developments in artificial intelligence</p>
      </div>

      <FilterBar filters={filters} onFilterChange={handleFilterChange} />

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-800 rounded-lg p-4 mb-6">
          <p>{error}</p>
          <p className="text-sm mt-2">
            Note: You need to set up your API keys in the .env.local file to fetch real data from Twitter, YouTube, and Google. 
            Currently showing mock data as a fallback.
          </p>
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center py-20">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
        </div>
      ) : newsItems.length === 0 ? (
        <div className="text-center py-20">
          <p className="text-gray-500 text-lg">No news items found matching your filters.</p>
          <button 
            onClick={() => setFilters({ category: 'All', timeRange: 'all', sources: [] })}
            className="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors"
          >
            Reset Filters
          </button>
        </div>
      ) : (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
          {newsItems.map((item) => (
            <NewsTile 
              key={item.id} 
              item={item} 
              onClick={handleTileClick} 
            />
          ))}
        </div>
      )}

      {selectedItem && (
        <NewsDetail item={selectedItem} onClose={handleCloseDetail} />
      )}
    </Layout>
  );
} 