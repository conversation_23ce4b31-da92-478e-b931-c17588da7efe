/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "os":
/*!*********************!*\
  !*** external "os" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("os");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tty":
/*!**********************!*\
  !*** external "tty" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tty");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fapp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Ferror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Flayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Frender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*******************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*******************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(ssr)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMiUyRlVzZXJzJTJGYmhhcnRlbmR1a3VtYXIlMkZEb2N1bWVudHMlMkZDb2RpbmclMkZkZW1vcyUyRmFpX25ld3MlMkZzcmMlMkZhcHAlMkZwYWdlLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEsZ0pBQTBHIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktbmV3cy1kYXNoYm9hcmQvP2IxOTQiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCIvVXNlcnMvYmhhcnRlbmR1a3VtYXIvRG9jdW1lbnRzL0NvZGluZy9kZW1vcy9haV9uZXdzL3NyYy9hcHAvcGFnZS50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fsrc%2Fapp%2Fpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fsrc%2Fstyles%2Fglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*************************************************************************************************************************************************************************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(ssr)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_Layout__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/Layout */ \"(ssr)/./src/components/Layout.tsx\");\n/* harmony import */ var _components_NewsTile__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/NewsTile */ \"(ssr)/./src/components/NewsTile.tsx\");\n/* harmony import */ var _components_NewsDetail__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/NewsDetail */ \"(ssr)/./src/components/NewsDetail.tsx\");\n/* harmony import */ var _components_FilterBar__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/FilterBar */ \"(ssr)/./src/components/FilterBar.tsx\");\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nfunction Home() {\n    const [newsItems, setNewsItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedItem, setSelectedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [filters, setFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        category: \"All\",\n        timeRange: \"all\",\n        sources: []\n    });\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchNews = async ()=>{\n            setLoading(true);\n            setError(null);\n            try {\n                const items = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_6__.getNewsItems)(filters);\n                setNewsItems(items);\n            } catch (error) {\n                console.error(\"Error fetching news:\", error);\n                setError(\"Failed to load news. Please check your API keys and try again.\");\n            } finally{\n                setLoading(false);\n            }\n        };\n        fetchNews();\n    }, [\n        filters\n    ]);\n    const handleTileClick = (item)=>{\n        setSelectedItem(item);\n    };\n    const handleCloseDetail = ()=>{\n        setSelectedItem(null);\n    };\n    const handleFilterChange = (newFilters)=>{\n        setFilters(newFilters);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Layout__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-3xl font-bold text-gray-900 mb-2\",\n                        children: \"AI News Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                        lineNumber: 56,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Stay updated with the latest developments in artificial intelligence\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_FilterBar__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                filters: filters,\n                onFilterChange: handleFilterChange\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                lineNumber: 60,\n                columnNumber: 7\n            }, this),\n            error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-red-50 border border-red-200 text-red-800 rounded-lg p-4 mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                        lineNumber: 64,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-sm mt-2\",\n                        children: \"Note: You need to set up your API keys in the .env.local file to fetch real data from Twitter, YouTube, and Google. Currently showing mock data as a fallback.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                lineNumber: 63,\n                columnNumber: 9\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center py-20\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-primary\"\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                    lineNumber: 74,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, this) : newsItems.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-20\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500 text-lg\",\n                        children: \"No news items found matching your filters.\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                        lineNumber: 78,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>setFilters({\n                                category: \"All\",\n                                timeRange: \"all\",\n                                sources: []\n                            }),\n                        className: \"mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-blue-600 transition-colors\",\n                        children: \"Reset Filters\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                        lineNumber: 79,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                lineNumber: 77,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6\",\n                children: newsItems.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewsTile__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                        item: item,\n                        onClick: handleTileClick\n                    }, item.id, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                        lineNumber: 89,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, this),\n            selectedItem && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NewsDetail__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                item: selectedItem,\n                onClose: handleCloseDetail\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n                lineNumber: 99,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx\",\n        lineNumber: 54,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/app/page.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/FilterBar.tsx":
/*!**************************************!*\
  !*** ./src/components/FilterBar.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\n\nconst categories = [\n    \"All\",\n    \"AI Models\",\n    \"Research Papers\",\n    \"Tools & Applications\",\n    \"Companies\",\n    \"Industry News\",\n    \"Tutorials\",\n    \"Events\"\n];\nconst timeRanges = [\n    {\n        value: \"day\",\n        label: \"Today\"\n    },\n    {\n        value: \"week\",\n        label: \"This Week\"\n    },\n    {\n        value: \"month\",\n        label: \"This Month\"\n    },\n    {\n        value: \"year\",\n        label: \"This Year\"\n    },\n    {\n        value: \"all\",\n        label: \"All Time\"\n    }\n];\nconst FilterBar = ({ filters, onFilterChange })=>{\n    const [sources, setSources] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const fetchSources = async ()=>{\n            setIsLoading(true);\n            try {\n                const sourcesData = await (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.getNewsSources)();\n                setSources(sourcesData);\n            } catch (error) {\n                console.error(\"Error fetching sources:\", error);\n            } finally{\n                setIsLoading(false);\n            }\n        };\n        fetchSources();\n    }, []);\n    const handleSourceToggle = (sourceId)=>{\n        const source = sources.find((s)=>s.id === sourceId);\n        if (!source) return;\n        const currentSources = filters.sources || [];\n        const sourceName = source.name;\n        if (currentSources.includes(sourceName)) {\n            onFilterChange({\n                ...filters,\n                sources: currentSources.filter((s)=>s !== sourceName)\n            });\n        } else {\n            onFilterChange({\n                ...filters,\n                sources: [\n                    ...currentSources,\n                    sourceName\n                ]\n            });\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white shadow-sm rounded-lg p-4 mb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"space-y-1\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                            className: \"text-sm font-medium text-gray-700\",\n                            children: \"Category\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-wrap gap-2\",\n                            children: categories.map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    className: `px-3 py-1 text-sm rounded-full transition-colors ${filters.category === category ? \"bg-primary text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"}`,\n                                    onClick: ()=>onFilterChange({\n                                            ...filters,\n                                            category\n                                        }),\n                                    children: category\n                                }, category, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                                    lineNumber: 76,\n                                    columnNumber: 15\n                                }, undefined))\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                            lineNumber: 74,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-col md:flex-row md:items-end md:justify-between space-y-4 md:space-y-0 md:space-x-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"Sources\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                                    lineNumber: 93,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2 mt-1\",\n                                    children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-gray-500\",\n                                        children: \"Loading sources...\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                                        lineNumber: 96,\n                                        columnNumber: 17\n                                    }, undefined) : sources.map((source)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: `flex items-center px-3 py-1 text-sm rounded-full transition-colors ${filters.sources?.includes(source.name) ? \"bg-primary text-white\" : \"bg-gray-100 text-gray-700 hover:bg-gray-200\"}`,\n                                            onClick: ()=>handleSourceToggle(source.id),\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-1\",\n                                                    children: source.icon\n                                                }, void 0, false, {\n                                                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                                                    lineNumber: 108,\n                                                    columnNumber: 21\n                                                }, undefined),\n                                                source.name\n                                            ]\n                                        }, source.id, true, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                                            lineNumber: 99,\n                                            columnNumber: 19\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                                    lineNumber: 94,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                            lineNumber: 92,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"md:w-48\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"text-sm font-medium text-gray-700\",\n                                    children: \"Time Range\"\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                    className: \"mt-1 block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-primary focus:border-primary sm:text-sm rounded-md\",\n                                    value: filters.timeRange || \"all\",\n                                    onChange: (e)=>onFilterChange({\n                                            ...filters,\n                                            timeRange: e.target.value\n                                        }),\n                                    children: timeRanges.map((range)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                            value: range.value,\n                                            children: range.label\n                                        }, range.value, false, {\n                                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 17\n                                        }, undefined))\n                                }, void 0, false, {\n                                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                                    lineNumber: 118,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n            lineNumber: 71,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/FilterBar.tsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (FilterBar);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/FilterBar.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/Layout.tsx":
/*!***********************************!*\
  !*** ./src/components/Layout.tsx ***!
  \***********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst Layout = ({ children })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen flex flex-col\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-primary text-2xl font-bold\",\n                                        children: \"AI\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                                        lineNumber: 14,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-secondary text-2xl font-bold\",\n                                        children: \"NewsHub\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                                        lineNumber: 15,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                                lineNumber: 13,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-sm text-gray-500\",\n                                children: \"Stay Updated with AI Developments\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                                lineNumber: 17,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                        lineNumber: 12,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                    lineNumber: 11,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"flex-grow\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 py-6\",\n                    children: children\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white shadow-sm mt-auto\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col md:flex-row justify-between items-center text-sm text-gray-500\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    \"\\xa9 \",\n                                    new Date().getFullYear(),\n                                    \" AI NewsHub\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: \"Made with ❤️ for AI enthusiasts\"\n                            }, void 0, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                                lineNumber: 34,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                        lineNumber: 32,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                    lineNumber: 31,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n                lineNumber: 30,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/Layout.tsx\",\n        lineNumber: 9,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Layout);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/Layout.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NewsDetail.tsx":
/*!***************************************!*\
  !*** ./src/components/NewsDetail.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\n\nconst NewsDetail = ({ item, onClose })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"card w-full max-w-2xl max-h-[90vh] overflow-y-auto\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-between items-start mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"card-title\",\n                            children: item.title\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: onClose,\n                            className: \"text-gray-500 hover:text-gray-800\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                xmlns: \"http://www.w3.org/2000/svg\",\n                                width: \"24\",\n                                height: \"24\",\n                                viewBox: \"0 0 24 24\",\n                                fill: \"none\",\n                                stroke: \"currentColor\",\n                                strokeWidth: \"2\",\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                        x1: \"18\",\n                                        y1: \"6\",\n                                        x2: \"6\",\n                                        y2: \"18\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                                        lineNumber: 21,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"line\", {\n                                        x1: \"6\",\n                                        y1: \"6\",\n                                        x2: \"18\",\n                                        y2: \"18\"\n                                    }, void 0, false, {\n                                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                                        lineNumber: 22,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                                lineNumber: 20,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                            lineNumber: 16,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-subtitle flex items-center justify-between\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: [\n                                item.source,\n                                \" • \",\n                                (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.formatDate)(item.date)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                            href: item.url,\n                            target: \"_blank\",\n                            rel: \"noopener noreferrer\",\n                            className: \"text-primary hover:underline\",\n                            children: \"Visit Source\"\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                            lineNumber: 29,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                    lineNumber: 27,\n                    columnNumber: 9\n                }, undefined),\n                item.imageUrl && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: item.imageUrl,\n                        alt: item.title,\n                        className: \"w-full h-48 object-cover rounded-lg\"\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                        lineNumber: 41,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                    lineNumber: 40,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"card-content mb-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold mb-2\",\n                            children: item.description\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: item.content\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex flex-wrap gap-2\",\n                    children: [\n                        item.tags.map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"badge badge-primary\",\n                                children: tag\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                                lineNumber: 56,\n                                columnNumber: 13\n                            }, undefined)),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"badge badge-secondary ml-auto\",\n                            children: item.category\n                        }, void 0, false, {\n                            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                            lineNumber: 58,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n                    lineNumber: 54,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsDetail.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewsDetail);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NewsDetail.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/NewsTile.tsx":
/*!*************************************!*\
  !*** ./src/components/NewsTile.tsx ***!
  \*************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_api__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/api */ \"(ssr)/./src/lib/api.ts\");\n\n\n\nconst NewsTile = ({ item, onClick })=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"tile group\",\n        onClick: ()=>onClick(item),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                className: \"tile-title group-hover:text-primary transition-colors\",\n                children: item.title\n            }, void 0, false, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsTile.tsx\",\n                lineNumber: 16,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-sm text-gray-500 mb-2\",\n                children: [\n                    item.source,\n                    \" • \",\n                    (0,_lib_api__WEBPACK_IMPORTED_MODULE_2__.formatDate)(item.date)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsTile.tsx\",\n                lineNumber: 17,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-x-1\",\n                        children: item.tags.slice(0, 2).map((tag, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"badge badge-primary\",\n                                children: tag\n                            }, index, false, {\n                                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsTile.tsx\",\n                                lineNumber: 21,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsTile.tsx\",\n                        lineNumber: 19,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        className: \"badge badge-secondary\",\n                        children: item.category\n                    }, void 0, false, {\n                        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsTile.tsx\",\n                        lineNumber: 24,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsTile.tsx\",\n                lineNumber: 18,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/components/NewsTile.tsx\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (NewsTile);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/NewsTile.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/api.ts":
/*!************************!*\
  !*** ./src/lib/api.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   getNewsItemById: () => (/* binding */ getNewsItemById),\n/* harmony export */   getNewsItems: () => (/* binding */ getNewsItems),\n/* harmony export */   getNewsSources: () => (/* binding */ getNewsSources)\n/* harmony export */ });\n/* harmony import */ var axios__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! axios */ \"(ssr)/./node_modules/axios/lib/axios.js\");\n/* harmony import */ var _barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! __barrel_optimize__?names=format,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/parseISO/index.js\");\n/* harmony import */ var _barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=format,parseISO!=!date-fns */ \"(ssr)/./node_modules/date-fns/esm/format/index.js\");\n\n\n// API configuration - in a real app, these would be in environment variables\nconst TWITTER_BEARER_TOKEN = \"your_twitter_api_token\";\nconst YOUTUBE_API_KEY = \"your_youtube_api_key\";\nconst GOOGLE_CSE_ID = \"your_google_cse_id\";\nconst GOOGLE_API_KEY = \"your_google_api_key\";\nconst BRAVE_SEARCH_API_KEY = \"your_brave_search_api_key\";\n// Helper function to create unique IDs\nconst createId = ()=>Math.random().toString(36).substring(2, 9);\n// Mock data for when API keys aren't configured\nconst mockNewsItems = [\n    {\n        id: \"1\",\n        title: \"OpenAI Releases GPT-5 with Breakthrough Reasoning Capabilities\",\n        description: \"The latest model shows 40% improvement in complex reasoning tasks.\",\n        content: \"OpenAI has announced GPT-5, their most advanced AI model to date. The new model demonstrates significant improvements in reasoning, code generation, and multimodal understanding. Independent evaluations show a 40% improvement in complex reasoning tasks compared to GPT-4.\",\n        source: \"Twitter - AI News Daily\",\n        url: \"https://twitter.com/aiTechDaily/status/1732456789\",\n        imageUrl: \"https://via.placeholder.com/300x200?text=GPT-5\",\n        date: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(),\n        category: \"AI Models\",\n        tags: [\n            \"GPT-5\",\n            \"OpenAI\",\n            \"Language Models\",\n            \"Twitter\"\n        ]\n    },\n    {\n        id: \"2\",\n        title: \"Google DeepMind Introduces AlphaCode 2 for Advanced Programming\",\n        description: \"New AI system can solve competitive programming problems at expert level.\",\n        content: \"Google DeepMind has unveiled AlphaCode 2, a next-generation AI system designed to write computer programs. The system can solve competitive programming problems at an expert level, outperforming 85% of human participants in programming contests.\",\n        source: \"YouTube - Google AI\",\n        url: \"https://www.youtube.com/watch?v=example123\",\n        imageUrl: \"https://via.placeholder.com/300x200?text=AlphaCode+2\",\n        date: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000).toISOString(),\n        category: \"AI Models\",\n        tags: [\n            \"AlphaCode\",\n            \"Google DeepMind\",\n            \"Programming AI\",\n            \"YouTube\",\n            \"Video\"\n        ]\n    },\n    {\n        id: \"3\",\n        title: \"Meta Releases LLAMA 3 as Open Source\",\n        description: \"High-performance large language model now available for researchers and developers.\",\n        content: \"Meta has released LLAMA 3, its latest large language model, as open source. The model is available in several sizes, from 7B to 70B parameters, and shows competitive performance with proprietary models. Researchers and developers can now access the model weights and integrate them into their applications.\",\n        source: \"Google - TechCrunch\",\n        url: \"https://techcrunch.com/example/llama3\",\n        imageUrl: \"https://via.placeholder.com/300x200?text=LLAMA+3\",\n        date: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),\n        category: \"AI Models\",\n        tags: [\n            \"LLAMA 3\",\n            \"Meta AI\",\n            \"Open Source AI\",\n            \"Google\"\n        ]\n    },\n    {\n        id: \"4\",\n        title: \"New Research Shows AI Can Detect Early Signs of Alzheimer Disease\",\n        description: \"Deep learning algorithm identifies subtle patterns in brain scans years before symptoms appear.\",\n        content: \"Researchers from Stanford University have developed a deep learning algorithm that can detect early signs of Alzheimer disease in brain scans. The algorithm can identify subtle patterns up to six years before clinical symptoms appear, potentially allowing for earlier intervention and treatment.\",\n        source: \"Google - Medical AI Journal\",\n        url: \"https://medicalaijournal.com/example/alzheimers\",\n        imageUrl: \"https://via.placeholder.com/300x200?text=AI+in+Medicine\",\n        date: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(),\n        category: \"Research Papers\",\n        tags: [\n            \"Healthcare AI\",\n            \"Medical Imaging\",\n            \"Neural Networks\",\n            \"Google\"\n        ]\n    },\n    {\n        id: \"5\",\n        title: \"Anthropic Secures $1B in Funding for Constitutional AI Research\",\n        description: \"Investment will accelerate development of safer AI systems.\",\n        content: \"Anthropic has secured $1 billion in new funding to continue its work on Constitutional AI, an approach to training AI systems that adhere to a set of principles. The company will use the funding to scale its research efforts and expand its team of AI safety researchers.\",\n        source: \"Twitter - Tech Investor\",\n        url: \"https://twitter.com/techinvestor/status/**********\",\n        imageUrl: \"https://via.placeholder.com/300x200?text=Anthropic\",\n        date: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000).toISOString(),\n        category: \"Companies\",\n        tags: [\n            \"Anthropic\",\n            \"AI Safety\",\n            \"Investment\",\n            \"Twitter\"\n        ]\n    },\n    {\n        id: \"6\",\n        title: \"New Tool Converts Natural Language to SQL Queries with 98% Accuracy\",\n        description: \"Open-source project simplifies database interactions for non-technical users.\",\n        content: \"A new open-source tool called SQLGenius can convert natural language to SQL queries with 98% accuracy. The tool uses a fine-tuned large language model specifically optimized for database operations, making it easier for non-technical users to interact with databases without knowing SQL syntax.\",\n        source: \"YouTube - Database Tech\",\n        url: \"https://www.youtube.com/watch?v=sqlgenius123\",\n        imageUrl: \"https://via.placeholder.com/300x200?text=SQLGenius\",\n        date: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(),\n        category: \"Tools & Applications\",\n        tags: [\n            \"SQL\",\n            \"Natural Language Processing\",\n            \"Developer Tools\",\n            \"YouTube\",\n            \"Video\"\n        ]\n    },\n    {\n        id: \"7\",\n        title: \"Tesla Unveils New AI-Powered Robotaxi Service\",\n        description: \"Autonomous taxis to launch in three major U.S. cities next year.\",\n        content: \"Tesla has unveiled its AI-powered Robotaxi service, set to launch in San Francisco, Austin, and Miami next year. The vehicles will operate without human drivers, using Tesla FSD (Full Self-Driving) AI system. The company claims the service will be 50% cheaper than current ride-sharing options.\",\n        source: \"Google - Electric Vehicle News\",\n        url: \"https://evnews.com/example/tesla-robotaxi\",\n        imageUrl: \"https://via.placeholder.com/300x200?text=Tesla+Robotaxi\",\n        date: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000).toISOString(),\n        category: \"Industry News\",\n        tags: [\n            \"Tesla\",\n            \"Autonomous Vehicles\",\n            \"FSD\",\n            \"Google\"\n        ]\n    },\n    {\n        id: \"8\",\n        title: \"IBM Watson Assistant Now Supports 45 Languages\",\n        description: \"Major upgrade expands global accessibility of conversational AI platform.\",\n        content: \"IBM has announced a major upgrade to Watson Assistant, its conversational AI platform, which now supports 45 languages. The expansion includes improved understanding of regional dialects and colloquialisms, making the platform more accessible to businesses around the world.\",\n        source: \"Twitter - IBM Developer\",\n        url: \"https://twitter.com/ibmdev/status/1725432109\",\n        imageUrl: \"https://via.placeholder.com/300x200?text=IBM+Watson\",\n        date: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000).toISOString(),\n        category: \"Tools & Applications\",\n        tags: [\n            \"IBM Watson\",\n            \"Multilingual AI\",\n            \"Conversational AI\",\n            \"Twitter\"\n        ]\n    }\n];\n// Twitter API service (X)\nasync function fetchTwitterPosts(query) {\n    try {\n        if (!TWITTER_BEARER_TOKEN) {\n            console.warn(\"Twitter API token not configured\");\n            return mockNewsItems.filter((item)=>item.source.includes(\"Twitter\"));\n        }\n        // Twitter v2 API to search for recent tweets\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`https://api.twitter.com/2/tweets/search/recent?query=${encodeURIComponent(query)}&tweet.fields=created_at,public_metrics,entities&expansions=author_id&user.fields=name,profile_image_url`, {\n            headers: {\n                Authorization: `Bearer ${TWITTER_BEARER_TOKEN}`\n            }\n        });\n        if (!response.data.data || response.data.data.length === 0) {\n            return [];\n        }\n        const users = response.data.includes.users.reduce((acc, user)=>{\n            acc[user.id] = user;\n            return acc;\n        }, {});\n        return response.data.data.map((tweet)=>{\n            const user = users[tweet.author_id];\n            const tags = tweet.entities?.hashtags ? tweet.entities.hashtags.map((tag)=>tag.tag) : [];\n            return {\n                id: createId(),\n                title: tweet.text.length > 60 ? tweet.text.substring(0, 60) + \"...\" : tweet.text,\n                description: tweet.text,\n                content: tweet.text,\n                source: `Twitter - ${user.name}`,\n                url: `https://twitter.com/${user.username}/status/${tweet.id}`,\n                imageUrl: user.profile_image_url,\n                date: tweet.created_at,\n                category: \"Industry News\",\n                tags: [\n                    ...tags,\n                    \"Twitter\"\n                ]\n            };\n        });\n    } catch (error) {\n        console.error(\"Error fetching Twitter data:\", error);\n        return mockNewsItems.filter((item)=>item.source.includes(\"Twitter\"));\n    }\n}\n// YouTube API service\nasync function fetchYouTubeVideos(query) {\n    try {\n        if (!YOUTUBE_API_KEY) {\n            console.warn(\"YouTube API key not configured\");\n            return mockNewsItems.filter((item)=>item.source.includes(\"YouTube\"));\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`https://www.googleapis.com/youtube/v3/search?part=snippet&q=${encodeURIComponent(query)}&type=video&maxResults=10&order=date&relevanceLanguage=en&key=${YOUTUBE_API_KEY}`);\n        if (!response.data.items || response.data.items.length === 0) {\n            return [];\n        }\n        return response.data.items.map((item)=>{\n            const { snippet, id } = item;\n            return {\n                id: createId(),\n                title: snippet.title,\n                description: snippet.description,\n                content: snippet.description,\n                source: `YouTube - ${snippet.channelTitle}`,\n                url: `https://www.youtube.com/watch?v=${id.videoId}`,\n                imageUrl: snippet.thumbnails.medium.url,\n                date: snippet.publishedAt,\n                category: \"Tutorials\",\n                tags: [\n                    \"YouTube\",\n                    \"Video\",\n                    \"AI\"\n                ]\n            };\n        });\n    } catch (error) {\n        console.error(\"Error fetching YouTube data:\", error);\n        return mockNewsItems.filter((item)=>item.source.includes(\"YouTube\"));\n    }\n}\n// Brave Search API service\nasync function fetchBraveSearch(query) {\n    try {\n        if (!BRAVE_SEARCH_API_KEY) {\n            console.warn(\"Brave Search API key not configured\");\n            return mockNewsItems.filter((item)=>item.source.includes(\"Brave\"));\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`https://api.search.brave.com/res/v1/web/search?q=${encodeURIComponent(query)}&search_lang=en&count=10&freshness=week`, {\n            headers: {\n                \"Accept\": \"application/json\",\n                \"Accept-Encoding\": \"gzip\",\n                \"X-Subscription-Token\": BRAVE_SEARCH_API_KEY\n            }\n        });\n        if (!response.data.web?.results || response.data.web.results.length === 0) {\n            return [];\n        }\n        return response.data.web.results.map((item)=>{\n            // Try to determine a category based on keywords in the title or description\n            let category = \"Industry News\";\n            const lowerTitle = item.title.toLowerCase();\n            const lowerDescription = item.description.toLowerCase();\n            if (lowerTitle.includes(\"research\") || lowerDescription.includes(\"research\")) {\n                category = \"Research Papers\";\n            } else if (lowerTitle.includes(\"model\") || lowerDescription.includes(\"model\")) {\n                category = \"AI Models\";\n            } else if (lowerTitle.includes(\"tool\") || lowerDescription.includes(\"tool\")) {\n                category = \"Tools & Applications\";\n            } else if (lowerTitle.includes(\"company\") || lowerDescription.includes(\"company\")) {\n                category = \"Companies\";\n            }\n            return {\n                id: createId(),\n                title: item.title,\n                description: item.description,\n                content: item.description,\n                source: `Brave - ${new URL(item.url).hostname.replace(\"www.\", \"\")}`,\n                url: item.url,\n                imageUrl: item.thumbnail?.src || null,\n                date: item.age ? new Date(Date.now() - parseInt(item.age) * 1000).toISOString() : new Date().toISOString(),\n                category,\n                tags: [\n                    \"Brave\",\n                    category,\n                    \"Web\"\n                ]\n            };\n        });\n    } catch (error) {\n        console.error(\"Error fetching Brave Search data:\", error);\n        return mockNewsItems.filter((item)=>item.source.includes(\"Brave\"));\n    }\n}\n// LinkedIn News Scraper (Note: LinkedIn doesn't offer a public API for this)\n// This is a simulated version that would need to be replaced with a proper server-side scraper\nasync function fetchLinkedInNews(query) {\n    try {\n        // In a real app, this would be a server-side API endpoint that scrapes LinkedIn\n        // Since LinkedIn doesn't have a public API for this purpose, we'd need to implement\n        // a custom scraper on the backend\n        // For now, return mock data for LinkedIn\n        return mockNewsItems.filter((item)=>item.category === \"Companies\" || item.category === \"Industry News\").map((item)=>({\n                ...item,\n                source: `LinkedIn - ${item.source.split(\" - \")[1] || \"Business News\"}`,\n                tags: [\n                    ...item.tags.filter((tag)=>tag !== \"Twitter\" && tag !== \"Google\"),\n                    \"LinkedIn\"\n                ]\n            }));\n    } catch (error) {\n        console.error(\"Error fetching LinkedIn data:\", error);\n        return [];\n    }\n}\n// Google Custom Search API service\nasync function fetchGoogleNews(query) {\n    try {\n        if (!GOOGLE_API_KEY || !GOOGLE_CSE_ID) {\n            console.warn(\"Google API credentials not configured\");\n            return mockNewsItems.filter((item)=>item.source.includes(\"Google\"));\n        }\n        const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`https://www.googleapis.com/customsearch/v1?key=${GOOGLE_API_KEY}&cx=${GOOGLE_CSE_ID}&q=${encodeURIComponent(query)}&sort=date&dateRestrict=m1`);\n        if (!response.data.items || response.data.items.length === 0) {\n            return [];\n        }\n        return response.data.items.map((item)=>{\n            // Try to determine a category based on keywords in the title or snippet\n            let category = \"Industry News\";\n            if (item.title.toLowerCase().includes(\"research\") || item.snippet.toLowerCase().includes(\"research\")) {\n                category = \"Research Papers\";\n            } else if (item.title.toLowerCase().includes(\"model\") || item.snippet.toLowerCase().includes(\"model\")) {\n                category = \"AI Models\";\n            } else if (item.title.toLowerCase().includes(\"tool\") || item.snippet.toLowerCase().includes(\"tool\")) {\n                category = \"Tools & Applications\";\n            }\n            return {\n                id: createId(),\n                title: item.title,\n                description: item.snippet,\n                content: item.snippet,\n                source: `Google - ${new URL(item.link).hostname.replace(\"www.\", \"\")}`,\n                url: item.link,\n                imageUrl: item.pagemap?.cse_image?.[0]?.src || null,\n                date: item.pagemap?.metatags?.[0]?.[\"article:published_time\"] || new Date().toISOString(),\n                category,\n                tags: [\n                    \"Google\",\n                    category\n                ]\n            };\n        });\n    } catch (error) {\n        console.error(\"Error fetching Google data:\", error);\n        return mockNewsItems.filter((item)=>item.source.includes(\"Google\"));\n    }\n}\n// Reddit API service for AI subreddits\nasync function fetchRedditPosts(query) {\n    try {\n        // Reddit doesn't require auth for this basic endpoint\n        const subreddits = [\n            \"artificial\",\n            \"MachineLearning\",\n            \"AINews\",\n            \"GPT3\"\n        ];\n        const allPosts = [];\n        for (const subreddit of subreddits){\n            try {\n                const response = await axios__WEBPACK_IMPORTED_MODULE_0__[\"default\"].get(`https://www.reddit.com/r/${subreddit}/search.json?q=${encodeURIComponent(query)}&restrict_sr=on&sort=new&t=week&limit=5`);\n                if (response.data?.data?.children) {\n                    const posts = response.data.data.children.filter((post)=>post.data && !post.data.is_self).map((post)=>{\n                        const data = post.data;\n                        let category = \"Industry News\";\n                        if (data.link_flair_text) {\n                            const flair = data.link_flair_text.toLowerCase();\n                            if (flair.includes(\"research\")) {\n                                category = \"Research Papers\";\n                            } else if (flair.includes(\"project\") || flair.includes(\"tool\")) {\n                                category = \"Tools & Applications\";\n                            } else if (flair.includes(\"discussion\")) {\n                                category = \"Industry News\";\n                            }\n                        }\n                        return {\n                            id: createId(),\n                            title: data.title,\n                            description: data.selftext || `Posted by u/${data.author} in r/${data.subreddit}`,\n                            content: data.selftext || data.url,\n                            source: `Reddit - r/${data.subreddit}`,\n                            url: `https://www.reddit.com${data.permalink}`,\n                            imageUrl: data.thumbnail !== \"self\" && data.thumbnail !== \"default\" ? data.thumbnail : null,\n                            date: new Date(data.created_utc * 1000).toISOString(),\n                            category,\n                            tags: [\n                                \"Reddit\",\n                                data.subreddit,\n                                category\n                            ]\n                        };\n                    });\n                    allPosts.push(...posts);\n                }\n            } catch (error) {\n                console.error(`Error fetching from r/${subreddit}:`, error);\n            }\n        }\n        return allPosts;\n    } catch (error) {\n        console.error(\"Error fetching Reddit data:\", error);\n        return [];\n    }\n}\n// Main API function to fetch all news sources\nasync function getNewsItems(filters) {\n    try {\n        // Define search queries for AI news\n        const query = \"artificial intelligence OR machine learning OR AI models OR deep learning OR LLM OR GPT OR language model OR neural network OR AI research\";\n        let allItems = [];\n        // Check if any API keys are configured\n        const hasApiKeys = TWITTER_BEARER_TOKEN || YOUTUBE_API_KEY || GOOGLE_API_KEY && GOOGLE_CSE_ID || BRAVE_SEARCH_API_KEY;\n        if (hasApiKeys) {\n            // Fetch data from multiple sources in parallel\n            const [twitterPosts, youtubeVideos, googleNews, braveResults, redditPosts, linkedInNews] = await Promise.all([\n                fetchTwitterPosts(query),\n                fetchYouTubeVideos(query),\n                fetchGoogleNews(query),\n                fetchBraveSearch(query),\n                fetchRedditPosts(query),\n                fetchLinkedInNews(query)\n            ]);\n            // Combine all results\n            allItems = [\n                ...twitterPosts,\n                ...youtubeVideos,\n                ...googleNews,\n                ...braveResults,\n                ...redditPosts,\n                ...linkedInNews\n            ];\n        } else {\n            // If no API keys are configured, use mock data\n            console.warn(\"No API keys configured, using mock data\");\n            allItems = [\n                ...mockNewsItems\n            ];\n        }\n        // Apply filters\n        if (filters) {\n            if (filters.category && filters.category !== \"All\") {\n                allItems = allItems.filter((item)=>item.category === filters.category);\n            }\n            if (filters.sources && filters.sources.length > 0) {\n                allItems = allItems.filter((item)=>{\n                    const itemSource = item.source.split(\" - \")[0].toLowerCase();\n                    return filters.sources.some((source)=>source.toLowerCase() === itemSource);\n                });\n            }\n            if (filters.timeRange && filters.timeRange !== \"all\") {\n                const now = new Date();\n                let cutoffDate = new Date();\n                switch(filters.timeRange){\n                    case \"day\":\n                        cutoffDate.setDate(now.getDate() - 1);\n                        break;\n                    case \"week\":\n                        cutoffDate.setDate(now.getDate() - 7);\n                        break;\n                    case \"month\":\n                        cutoffDate.setMonth(now.getMonth() - 1);\n                        break;\n                    case \"year\":\n                        cutoffDate.setFullYear(now.getFullYear() - 1);\n                        break;\n                }\n                allItems = allItems.filter((item)=>new Date(item.date) >= cutoffDate);\n            }\n        }\n        // Remove duplicates (based on title similarity)\n        const uniqueItems = [];\n        const titles = new Set();\n        for (const item of allItems){\n            // Normalize title for comparison\n            const normalizedTitle = item.title.toLowerCase().replace(/\\W+/g, \" \").trim();\n            // Check if we already have a similar title\n            let isDuplicate = false;\n            for (const existingTitle of titles){\n                // Simple string similarity check\n                if (normalizedTitle.includes(existingTitle) || existingTitle.includes(normalizedTitle)) {\n                    isDuplicate = true;\n                    break;\n                }\n            }\n            if (!isDuplicate) {\n                titles.add(normalizedTitle);\n                uniqueItems.push(item);\n            }\n        }\n        // Sort by date (newest first)\n        return uniqueItems.sort((a, b)=>new Date(b.date).getTime() - new Date(a.date).getTime());\n    } catch (error) {\n        console.error(\"Error fetching news data:\", error);\n        return mockNewsItems;\n    }\n}\n// Get a specific news item by ID\nasync function getNewsItemById(id) {\n    try {\n        // In a real app, you would fetch this from a database or API\n        // For now, we'll fetch all items and find the matching one\n        const allItems = await getNewsItems();\n        return allItems.find((item)=>item.id === id) || null;\n    } catch (error) {\n        console.error(\"Error fetching news item by ID:\", error);\n        return null;\n    }\n}\n// Get available news sources\nasync function getNewsSources() {\n    return [\n        {\n            id: \"1\",\n            name: \"Twitter\",\n            url: \"https://twitter.com\",\n            type: \"tweet\",\n            icon: \"\\uD83D\\uDC26\"\n        },\n        {\n            id: \"2\",\n            name: \"YouTube\",\n            url: \"https://youtube.com\",\n            type: \"video\",\n            icon: \"\\uD83D\\uDCFA\"\n        },\n        {\n            id: \"3\",\n            name: \"Google\",\n            url: \"https://google.com\",\n            type: \"article\",\n            icon: \"\\uD83D\\uDD0D\"\n        },\n        {\n            id: \"4\",\n            name: \"Brave\",\n            url: \"https://search.brave.com\",\n            type: \"article\",\n            icon: \"\\uD83E\\uDD81\"\n        },\n        {\n            id: \"5\",\n            name: \"Reddit\",\n            url: \"https://reddit.com\",\n            type: \"post\",\n            icon: \"\\uD83D\\uDC7D\"\n        },\n        {\n            id: \"6\",\n            name: \"LinkedIn\",\n            url: \"https://linkedin.com\",\n            type: \"post\",\n            icon: \"\\uD83D\\uDCBC\"\n        }\n    ];\n}\n// Format date for display\nfunction formatDate(dateString) {\n    try {\n        const date = (0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(dateString);\n        return (0,_barrel_optimize_names_format_parseISO_date_fns__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(date, \"MMM d, yyyy\");\n    } catch (error) {\n        // Fallback if parsing fails\n        return dateString;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/api.ts\n");

/***/ }),

/***/ "(rsc)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"fbb5c1e03c04\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vYWktbmV3cy1kYXNoYm9hcmQvLi9zcmMvc3R5bGVzL2dsb2JhbHMuY3NzPzU0YTUiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCJmYmI1YzFlMDNjMDRcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/styles/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(rsc)/./src/styles/globals.css\");\n\n\nconst metadata = {\n    title: \"AI NewsHub - Stay Updated with AI Developments\",\n    description: \"A dashboard to stay updated with the latest AI news, developments, and tools\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            children: children\n        }, void 0, false, {\n            fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/layout.tsx\",\n            lineNumber: 16,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"/Users/<USER>/Documents/Coding/demos/ai_news/src/app/layout.tsx\",\n        lineNumber: 15,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2xheW91dC50c3giLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQThCO0FBR3ZCLE1BQU1BLFdBQXFCO0lBQ2hDQyxPQUFPO0lBQ1BDLGFBQWE7QUFDZixFQUFFO0FBRWEsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7a0JBQ1QsNEVBQUNDO3NCQUFNSDs7Ozs7Ozs7Ozs7QUFHYiIsInNvdXJjZXMiOlsid2VicGFjazovL2FpLW5ld3MtZGFzaGJvYXJkLy4vc3JjL2FwcC9sYXlvdXQudHN4PzU3YTkiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0ICdAL3N0eWxlcy9nbG9iYWxzLmNzcyc7XG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCc7XG5cbmV4cG9ydCBjb25zdCBtZXRhZGF0YTogTWV0YWRhdGEgPSB7XG4gIHRpdGxlOiAnQUkgTmV3c0h1YiAtIFN0YXkgVXBkYXRlZCB3aXRoIEFJIERldmVsb3BtZW50cycsXG4gIGRlc2NyaXB0aW9uOiAnQSBkYXNoYm9hcmQgdG8gc3RheSB1cGRhdGVkIHdpdGggdGhlIGxhdGVzdCBBSSBuZXdzLCBkZXZlbG9wbWVudHMsIGFuZCB0b29scycsXG59O1xuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBSb290TGF5b3V0KHtcbiAgY2hpbGRyZW4sXG59OiB7XG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG59KSB7XG4gIHJldHVybiAoXG4gICAgPGh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8Ym9keT57Y2hpbGRyZW59PC9ib2R5PlxuICAgIDwvaHRtbD5cbiAgKTtcbn0gIl0sIm5hbWVzIjpbIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsIlJvb3RMYXlvdXQiLCJjaGlsZHJlbiIsImh0bWwiLCJsYW5nIiwiYm9keSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`/Users/<USER>/Documents/Coding/demos/ai_news/src/app/page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/axios","vendor-chunks/date-fns","vendor-chunks/asynckit","vendor-chunks/math-intrinsics","vendor-chunks/es-errors","vendor-chunks/@swc","vendor-chunks/call-bind-apply-helpers","vendor-chunks/debug","vendor-chunks/get-proto","vendor-chunks/mime-db","vendor-chunks/has-symbols","vendor-chunks/gopd","vendor-chunks/function-bind","vendor-chunks/form-data","vendor-chunks/follow-redirects","vendor-chunks/@babel","vendor-chunks/supports-color","vendor-chunks/proxy-from-env","vendor-chunks/ms","vendor-chunks/mime-types","vendor-chunks/hasown","vendor-chunks/has-tostringtag","vendor-chunks/has-flag","vendor-chunks/get-intrinsic","vendor-chunks/es-set-tostringtag","vendor-chunks/es-object-atoms","vendor-chunks/es-define-property","vendor-chunks/dunder-proto","vendor-chunks/delayed-stream","vendor-chunks/combined-stream"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news%2Fsrc%2Fapp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=%2FUsers%2Fbhartendukumar%2FDocuments%2FCoding%2Fdemos%2Fai_news&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();