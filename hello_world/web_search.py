#!/usr/bin/env python3
"""
Minimal Ollama tool-use demo: the model can call web_search()
and then completes its answer with the returned JSON.
Only 40-ish lines; no async, no extra bells & whistles.
"""
import json, requests

# --- 0.  Tiny helper --------------------------------------------------
def web_search(query: str) -> dict:
    """
    Free, no-key DuckDuckGo Instant Answer.
    Returns one short snippet we can feed back to the model.
    """
    url = "https://api.duckduckgo.com/"
    r = requests.get(url, params={"q": query, "format": "json"})
    r.raise_for_status()
    data = r.json()
    return {"snippet": data.get("AbstractText") or "No answer found."}

# --- 1.  Tool schema (OpenAI format) ----------------------------------
search_tool = {
    "type": "function",
    "function": {
        "name": "web_search",
        "description": "Look up a fact or recent info on the web.",
        "parameters": {
            "type": "object",
            "properties": {
                "query": {"type": "string"}
            },
            "required": ["query"],
        },
    },
}

# --- 2.  First request -------------------------------------------------
messages = [
    {"role": "user",
     "content": "Who is the current CEO of Nvidia and where was he born?"}
]

resp1 = requests.post(
    "http://localhost:11434/api/chat",
    json={
        "model": "gemma3:1b",     # any tool-capable model
        "messages": messages,
        "tools": [search_tool],
        "stream": False          # easier for a demo
    },
    timeout=60
).json()

# --- 3.  If the model asked to use the tool, do it --------------------
tool_calls = resp1["message"].get("tool_calls", [])
if tool_calls:
    for call in tool_calls:
        if call["function"]["name"] == "web_search":
            result = web_search(**call["function"]["arguments"])

            # feed the result straight back in
            messages += [
                resp1["message"],
                {
                    "role": "tool",
                    "name": "web_search",
                    "content": json.dumps(result)
                },
            ]

            resp2 = requests.post(
                "http://localhost:11434/api/chat",
                json={
                    "model": "llama3.1",
                    "messages": messages,
                    "stream": False
                },
                timeout=60
            ).json()

            print("\n🟢 FINAL ANSWER:\n", resp2["message"]["content"])
            break
else:
    # model answered without needing a search
    print("\n🟢 ANSWER (no tool call):\n", resp1["message"]["content"])