import requests
import json

# Placeholder for your Gemini API Key
API_KEY = "YOUR_GEMINI_API_KEY"

# Gemini API endpoint for the gemini-2.0-flash model
GEMINI_API_URL = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-2.0-flash:generateContent?key={API_KEY}"

def invoke_gemini_flash():
    """
    Sends a request to the Gemini 2.0 Flash model and prints the response.
    """
    if API_KEY == "YOUR_GEMINI_API_KEY":
        print("Please replace 'YOUR_GEMINI_API_KEY' with your actual Gemini API key in the script.")
        return

    headers = {
        "Content-Type": "application/json"
    }

    # Basic prompt structure for Gemini
    # You can customize the prompt and parameters as needed.
    # Refer to Gemini API documentation for more details:
    # https://ai.google.dev/docs/gemini_api_overview
    payload = {
        "contents": [
            {
                "parts": [
                    {
                        "text": "Write a short story about a friendly robot exploring a new planet."
                    }
                ]
            }
        ],
        "generationConfig": {
            "temperature": 0.7,
            "topK": 1,
            "topP": 1,
            "maxOutputTokens": 2048,
            "stopSequences": []
        },
        "safetySettings": [ # Optional: Adjust safety settings as needed
            {
                "category": "HARM_CATEGORY_HARASSMENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_HATE_SPEECH",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_SEXUALLY_EXPLICIT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            },
            {
                "category": "HARM_CATEGORY_DANGEROUS_CONTENT",
                "threshold": "BLOCK_MEDIUM_AND_ABOVE"
            }
        ]
    }

    try:
        response = requests.post(GEMINI_API_URL, headers=headers, json=payload)
        response.raise_for_status()  # Raises an exception for bad status codes (4xx or 5xx)

        response_data = response.json()

        # Extract and print the generated text
        # The structure of the response might vary, check Gemini API documentation
        if "candidates" in response_data and response_data["candidates"]:
            generated_text = response_data["candidates"][0]["content"]["parts"][0]["text"]
            print("Gemini Response:")
            print(generated_text)
        else:
            print("Could not extract generated text from the response.")
            print("Full response:", json.dumps(response_data, indent=2))

    except requests.exceptions.RequestException as e:
        print(f"An error occurred: {e}")
        if hasattr(e, 'response') and e.response is not None:
            print(f"Response status code: {e.response.status_code}")
            try:
                print(f"Response content: {e.response.json()}")
            except json.JSONDecodeError:
                print(f"Response content: {e.response.text}")

if __name__ == "__main__":
    invoke_gemini_flash()