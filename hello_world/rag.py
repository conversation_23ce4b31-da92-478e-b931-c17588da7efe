import requests
import numpy as np
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity

# Sample knowledge base - in real RAG, this would be much larger
documents = [
    "Python is a high-level programming language known for its simplicity and readability.",
    "Machine learning is a subset of artificial intelligence that uses algorithms to learn patterns.",
    "Ollama is a tool for running large language models locally on your machine.",
    "Vector databases store embeddings and enable semantic search capabilities.",
    "RAG combines retrieval of relevant documents with language model generation."
]

def create_vector_index(docs):
    """Create a simple TF-IDF vector index"""
    vectorizer = TfidfVectorizer()
    vectors = vectorizer.fit_transform(docs)
    return vectorizer, vectors

def retrieve_relevant_docs(query, vectorizer, doc_vectors, docs, top_k=2):
    """Retrieve most relevant documents for a query"""
    query_vector = vectorizer.transform([query])
    similarities = cosine_similarity(query_vector, doc_vectors).flatten()
    top_indices = np.argsort(similarities)[-top_k:][::-1]
    return [docs[i] for i in top_indices]

def generate_with_context(query, context_docs):
    """Generate response using Ollama with retrieved context"""
    context = "\n".join(f"- {doc}" for doc in context_docs)
    prompt = f"""Context information:
{context}

Question: {query}
Answer based on the context above:"""

    body = {
        "model": "gemma3:1b",
        "prompt": prompt,
        "stream": False
    }

    response = requests.post("http://localhost:11434/api/generate", json=body)
    return response.json()["response"]

def rag_demo():
    """Demonstrate RAG: Retrieve relevant docs, then generate answer"""
    print("=== RAG Demo ===")

    # 1. Index documents
    vectorizer, doc_vectors = create_vector_index(documents)
    print("✓ Documents indexed")

    # 2. User query
    query = "What is Ollama and how does it work?"
    print(f"\nQuery: {query}")

    # 3. Retrieve relevant documents
    relevant_docs = retrieve_relevant_docs(query, vectorizer, doc_vectors, documents)
    print(f"\nRetrieved {len(relevant_docs)} relevant documents:")
    for i, doc in enumerate(relevant_docs, 1):
        print(f"{i}. {doc}")

    # 4. Generate answer with context
    print("\nGenerating answer...")
    answer = generate_with_context(query, relevant_docs)
    print(f"\nRAG Answer: {answer}")

if __name__ == "__main__":
    rag_demo()